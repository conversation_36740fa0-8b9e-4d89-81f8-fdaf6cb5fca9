'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from './ui/Button'
import { 
  X, 
  Type, 
  Palette, 
  Image as ImageIcon, 
  Sparkles, 
  Download, 
  RotateCcw, 
  Undo, 
  Save,
  Upload,
  Sliders,
  Wand2,
  Plus
} from 'lucide-react'
import * as fabric from 'fabric'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

interface FilterSettings {
  brightness: number
  contrast: number
  saturation: number
  blur: number
  sepia: boolean
  grayscale: boolean
}

interface TextSettings {
  text: string
  fontSize: number
  fontFamily: string
  color: string
  fontWeight: string
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [activeTab, setActiveTab] = useState<'text' | 'filters' | 'logo' | 'ai'>('text')
  const [isLoading, setIsLoading] = useState(true)
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  
  // Filter settings
  const [filters, setFilters] = useState<FilterSettings>({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0,
    sepia: false,
    grayscale: false
  })
  
  // Text settings
  const [textSettings, setTextSettings] = useState<TextSettings>({
    text: 'Your Text Here',
    fontSize: 40,
    fontFamily: 'Arial',
    color: '#ffffff',
    fontWeight: 'bold'
  })
  
  // AI editing
  const [aiPrompt, setAiPrompt] = useState('')
  const [isProcessingAI, setIsProcessingAI] = useState(false)
  const [selectedArea, setSelectedArea] = useState<fabric.Rect | null>(null)
  const [isSelecting, setIsSelecting] = useState(false)

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!isOpen || !canvasRef.current) return

    const canvas = new fabric.fabric.Canvas(canvasRef.current, {
      width: 800,
      height: 450,
      backgroundColor: '#000000'
    })

    fabricCanvasRef.current = canvas

    // Load the image
    fabric.Image.fromURL(imageUrl, (img) => {
      if (!img || !canvas) return
      
      // Scale image to fit canvas
      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()
      const imgWidth = img.width || 1
      const imgHeight = img.height || 1
      
      const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight)
      
      img.scale(scale)
      img.set({
        left: (canvasWidth - imgWidth * scale) / 2,
        top: (canvasHeight - imgHeight * scale) / 2,
        selectable: false,
        evented: false
      })
      
      canvas.add(img)
      canvas.sendToBack(img)
      canvas.renderAll()
      setIsLoading(false)
      
      // Save initial state
      saveToHistory()
    })

    return () => {
      canvas.dispose()
      fabricCanvasRef.current = null
    }
  }, [isOpen, imageUrl])

  const saveToHistory = useCallback(() => {
    if (!fabricCanvasRef.current) return
    
    const canvasState = JSON.stringify(fabricCanvasRef.current.toJSON())
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1)
      newHistory.push(canvasState)
      return newHistory
    })
    setHistoryIndex(prev => prev + 1)
  }, [historyIndex])

  const undo = useCallback(() => {
    if (historyIndex <= 0 || !fabricCanvasRef.current) return
    
    const prevState = history[historyIndex - 1]
    fabricCanvasRef.current.loadFromJSON(prevState, () => {
      fabricCanvasRef.current?.renderAll()
    })
    setHistoryIndex(prev => prev - 1)
  }, [history, historyIndex])

  const addText = () => {
    if (!fabricCanvasRef.current) return

    const text = new fabric.Text(textSettings.text, {
      left: 100,
      top: 100,
      fontSize: textSettings.fontSize,
      fontFamily: textSettings.fontFamily,
      fill: textSettings.color,
      fontWeight: textSettings.fontWeight,
      stroke: '#000000',
      strokeWidth: 2
    })

    fabricCanvasRef.current.add(text)
    fabricCanvasRef.current.setActiveObject(text)
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const applyFilters = () => {
    if (!fabricCanvasRef.current) return

    const objects = fabricCanvasRef.current.getObjects()
    const backgroundImage = objects.find(obj => obj.type === 'image')
    
    if (!backgroundImage) return

    const filterArray: fabric.IBaseFilter[] = []
    
    if (filters.brightness !== 0) {
      filterArray.push(new fabric.Image.filters.Brightness({ brightness: filters.brightness / 100 }))
    }
    
    if (filters.contrast !== 0) {
      filterArray.push(new fabric.Image.filters.Contrast({ contrast: filters.contrast / 100 }))
    }
    
    if (filters.saturation !== 0) {
      filterArray.push(new fabric.Image.filters.Saturation({ saturation: filters.saturation / 100 }))
    }
    
    if (filters.blur > 0) {
      filterArray.push(new fabric.Image.filters.Blur({ blur: filters.blur / 10 }))
    }
    
    if (filters.sepia) {
      filterArray.push(new fabric.Image.filters.Sepia())
    }
    
    if (filters.grayscale) {
      filterArray.push(new fabric.Image.filters.Grayscale())
    }

    ;(backgroundImage as fabric.Image).filters = filterArray
    ;(backgroundImage as fabric.Image).applyFilters()
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !fabricCanvasRef.current) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      fabric.Image.fromURL(result, (img) => {
        if (!img || !fabricCanvasRef.current) return
        
        img.scale(0.3)
        img.set({
          left: 50,
          top: 50
        })
        
        fabricCanvasRef.current.add(img)
        fabricCanvasRef.current.setActiveObject(img)
        fabricCanvasRef.current.renderAll()
        saveToHistory()
      })
    }
    reader.readAsDataURL(file)
  }

  const startAreaSelection = () => {
    if (!fabricCanvasRef.current) return
    
    setIsSelecting(true)
    fabricCanvasRef.current.defaultCursor = 'crosshair'
    
    let isDown = false
    let origX = 0
    let origY = 0
    let rect: fabric.Rect

    const onMouseDown = (o: fabric.IEvent) => {
      if (!fabricCanvasRef.current) return
      
      isDown = true
      const pointer = fabricCanvasRef.current.getPointer(o.e)
      origX = pointer.x
      origY = pointer.y
      
      rect = new fabric.Rect({
        left: origX,
        top: origY,
        originX: 'left',
        originY: 'top',
        width: 0,
        height: 0,
        fill: 'rgba(255, 0, 0, 0.3)',
        stroke: '#ff0000',
        strokeWidth: 2,
        selectable: false
      })
      
      fabricCanvasRef.current.add(rect)
    }

    const onMouseMove = (o: fabric.IEvent) => {
      if (!isDown || !fabricCanvasRef.current) return
      
      const pointer = fabricCanvasRef.current.getPointer(o.e)
      
      if (origX > pointer.x) {
        rect.set({ left: Math.abs(pointer.x) })
      }
      if (origY > pointer.y) {
        rect.set({ top: Math.abs(pointer.y) })
      }
      
      rect.set({ width: Math.abs(origX - pointer.x) })
      rect.set({ height: Math.abs(origY - pointer.y) })
      
      fabricCanvasRef.current.renderAll()
    }

    const onMouseUp = () => {
      if (!fabricCanvasRef.current) return
      
      isDown = false
      setSelectedArea(rect)
      setIsSelecting(false)
      fabricCanvasRef.current.defaultCursor = 'default'
      
      // Remove event listeners
      fabricCanvasRef.current.off('mouse:down', onMouseDown)
      fabricCanvasRef.current.off('mouse:move', onMouseMove)
      fabricCanvasRef.current.off('mouse:up', onMouseUp)
    }

    fabricCanvasRef.current.on('mouse:down', onMouseDown)
    fabricCanvasRef.current.on('mouse:move', onMouseMove)
    fabricCanvasRef.current.on('mouse:up', onMouseUp)
  }

  const processAIEdit = async () => {
    if (!aiPrompt.trim() || !selectedArea || !fabricCanvasRef.current) return

    setIsProcessingAI(true)

    try {
      // Get the current canvas as image data
      const canvasDataUrl = fabricCanvasRef.current.toDataURL({
        format: 'png',
        quality: 1
      })

      // Create mask data from selected area
      const maskCanvas = document.createElement('canvas')
      const maskCtx = maskCanvas.getContext('2d')

      if (!maskCtx) {
        throw new Error('Could not create mask canvas context')
      }

      // Set canvas size to match the fabric canvas
      maskCanvas.width = fabricCanvasRef.current.getWidth()
      maskCanvas.height = fabricCanvasRef.current.getHeight()

      // Fill with black (areas to keep)
      maskCtx.fillStyle = 'black'
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height)

      // Fill selected area with white (areas to inpaint)
      maskCtx.fillStyle = 'white'
      maskCtx.fillRect(
        selectedArea.left || 0,
        selectedArea.top || 0,
        selectedArea.width || 0,
        selectedArea.height || 0
      )

      const maskDataUrl = maskCanvas.toDataURL()

      // Call the AI inpainting API
      const response = await fetch('/api/ai-inpaint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: canvasDataUrl,
          maskData: maskDataUrl,
          prompt: aiPrompt,
          selectedArea: {
            x: selectedArea.left || 0,
            y: selectedArea.top || 0,
            width: selectedArea.width || 0,
            height: selectedArea.height || 0
          }
        })
      })

      const data = await response.json()

      if (data.success && data.imageUrl) {
        // Load the new image and replace the canvas content
        fabric.Image.fromURL(data.imageUrl, (img) => {
          if (!img || !fabricCanvasRef.current) return

          // Clear the canvas and add the new image
          fabricCanvasRef.current.clear()

          // Scale image to fit canvas
          const canvasWidth = fabricCanvasRef.current.getWidth()
          const canvasHeight = fabricCanvasRef.current.getHeight()
          const imgWidth = img.width || 1
          const imgHeight = img.height || 1

          const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight)

          img.scale(scale)
          img.set({
            left: (canvasWidth - imgWidth * scale) / 2,
            top: (canvasHeight - imgHeight * scale) / 2,
            selectable: false,
            evented: false
          })

          fabricCanvasRef.current.add(img)
          fabricCanvasRef.current.sendToBack(img)
          fabricCanvasRef.current.renderAll()

          saveToHistory()
        })
      } else {
        throw new Error(data.error || 'AI inpainting failed')
      }
    } catch (error) {
      console.error('AI inpainting error:', error)
      // You might want to show an error message to the user here
    } finally {
      setIsProcessingAI(false)
      if (selectedArea && fabricCanvasRef.current) {
        fabricCanvasRef.current.remove(selectedArea)
        fabricCanvasRef.current.renderAll()
        setSelectedArea(null)
      }
      setAiPrompt('')
    }
  }

  const handleSave = () => {
    if (!fabricCanvasRef.current) return
    
    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1
    })
    
    onSave(dataURL)
    onClose()
  }

  const handleDownload = () => {
    if (!fabricCanvasRef.current) return
    
    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1
    })
    
    const link = document.createElement('a')
    link.download = 'edited-thumbnail.png'
    link.href = dataURL
    link.click()
  }

  const resetCanvas = () => {
    if (history.length > 0 && fabricCanvasRef.current) {
      const initialState = history[0]
      fabricCanvasRef.current.loadFromJSON(initialState, () => {
        fabricCanvasRef.current?.renderAll()
      })
      setHistoryIndex(0)
      setFilters({
        brightness: 0,
        contrast: 0,
        saturation: 0,
        blur: 0,
        sepia: false,
        grayscale: false
      })
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="relative mx-4 w-full max-w-6xl h-[90vh] rounded-lg bg-bg-secondary border border-border-primary overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-border-primary p-4">
          <div className="flex items-center space-x-3">
            <Palette className="h-5 w-5 text-accent-primary" />
            <h2 className="text-lg font-semibold text-text-primary">Image Editor</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={undo} disabled={historyIndex <= 0}>
              <Undo className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={resetCanvas}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex h-full">
          {/* Toolbar */}
          <div className="w-80 border-r border-border-primary p-4 overflow-y-auto">
            {/* Tool Tabs */}
            <div className="grid grid-cols-4 gap-1 mb-6">
              {[
                { id: 'text', icon: Type, label: 'Text' },
                { id: 'filters', icon: Sliders, label: 'Filters' },
                { id: 'logo', icon: ImageIcon, label: 'Logo' },
                { id: 'ai', icon: Wand2, label: 'AI Edit' }
              ].map((tab) => (
                <Button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  variant={activeTab === tab.id ? 'primary' : 'secondary'}
                  size="sm"
                  className="flex flex-col items-center p-2 h-auto"
                >
                  <tab.icon className="h-4 w-4 mb-1" />
                  <span className="text-xs">{tab.label}</span>
                </Button>
              ))}
            </div>

            {/* Tool Content */}
            <div className="space-y-4">
              {activeTab === 'text' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Add Text</h3>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Text</label>
                      <input
                        type="text"
                        value={textSettings.text}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, text: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                        placeholder="Enter your text"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Size</label>
                      <input
                        type="range"
                        min="12"
                        max="120"
                        value={textSettings.fontSize}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontSize: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{textSettings.fontSize}px</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Family</label>
                      <select
                        value={textSettings.fontFamily}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontFamily: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Impact">Impact</option>
                        <option value="Comic Sans MS">Comic Sans MS</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Color</label>
                      <input
                        type="color"
                        value={textSettings.color}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, color: e.target.value }))}
                        className="w-full h-10 rounded border border-border-primary"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Weight</label>
                      <select
                        value={textSettings.fontWeight}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontWeight: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="normal">Normal</option>
                        <option value="bold">Bold</option>
                        <option value="bolder">Bolder</option>
                        <option value="lighter">Lighter</option>
                      </select>
                    </div>
                  </div>

                  <Button onClick={addText} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Text
                  </Button>
                </div>
              )}

              {activeTab === 'filters' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Image Filters</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Brightness</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.brightness}
                        onChange={(e) => setFilters(prev => ({ ...prev, brightness: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.brightness}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Contrast</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.contrast}
                        onChange={(e) => setFilters(prev => ({ ...prev, contrast: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.contrast}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Saturation</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.saturation}
                        onChange={(e) => setFilters(prev => ({ ...prev, saturation: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.saturation}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Blur</label>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        value={filters.blur}
                        onChange={(e) => setFilters(prev => ({ ...prev, blur: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.blur}px</span>
                    </div>

                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.sepia}
                          onChange={(e) => setFilters(prev => ({ ...prev, sepia: e.target.checked }))}
                          className="rounded"
                        />
                        <span className="text-sm text-text-secondary">Sepia</span>
                      </label>

                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.grayscale}
                          onChange={(e) => setFilters(prev => ({ ...prev, grayscale: e.target.checked }))}
                          className="rounded"
                        />
                        <span className="text-sm text-text-secondary">Grayscale</span>
                      </label>
                    </div>
                  </div>

                  <Button onClick={applyFilters} className="w-full">
                    <Sliders className="h-4 w-4 mr-2" />
                    Apply Filters
                  </Button>
                </div>
              )}

              {activeTab === 'logo' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Add Logo</h3>

                  <div className="border-2 border-dashed border-border-primary rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 mx-auto mb-2 text-text-tertiary" />
                    <p className="text-sm text-text-secondary mb-2">Upload your logo</p>
                    <p className="text-xs text-text-tertiary mb-4">PNG, JPG, SVG supported</p>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                    />
                    <label htmlFor="logo-upload">
                      <Button as="span" size="sm">
                        Choose File
                      </Button>
                    </label>
                  </div>

                  <div className="text-xs text-text-tertiary">
                    <p>• Drag and resize the logo after uploading</p>
                    <p>• Use corner handles to maintain aspect ratio</p>
                    <p>• Double-click to edit transparency</p>
                  </div>
                </div>
              )}

              {activeTab === 'ai' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">AI Edit</h3>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-text-secondary mb-2">1. Select an area to edit</p>
                      <Button
                        onClick={startAreaSelection}
                        disabled={isSelecting}
                        variant="secondary"
                        className="w-full"
                      >
                        {isSelecting ? 'Selecting...' : 'Select Area'}
                      </Button>
                    </div>

                    {selectedArea && (
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          2. Describe what you want to add/change
                        </label>
                        <textarea
                          value={aiPrompt}
                          onChange={(e) => setAiPrompt(e.target.value)}
                          placeholder="e.g., add sunglasses, change background to beach, add fire effects..."
                          className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary resize-none"
                          rows={3}
                        />
                      </div>
                    )}

                    {selectedArea && (
                      <Button
                        onClick={processAIEdit}
                        disabled={!aiPrompt.trim() || isProcessingAI}
                        className="w-full"
                      >
                        {isProcessingAI ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-white/20 border-t-white rounded-full mr-2" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4 mr-2" />
                            Apply AI Edit
                          </>
                        )}
                      </Button>
                    )}
                  </div>

                  <div className="text-xs text-text-tertiary">
                    <p>• Select the area you want to modify</p>
                    <p>• Describe your desired changes clearly</p>
                    <p>• AI processing may take 30-60 seconds</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 flex items-center justify-center p-4">
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin h-6 w-6 border-2 border-accent-primary border-t-transparent rounded-full" />
                  <span>Loading image...</span>
                </div>
              ) : (
                <canvas ref={canvasRef} className="border border-border-primary rounded" />
              )}
            </div>

            {/* Bottom Actions */}
            <div className="border-t border-border-primary p-4 flex justify-between">
              <div className="flex space-x-2">
                <Button variant="secondary" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
              <div className="flex space-x-2">
                <Button variant="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
