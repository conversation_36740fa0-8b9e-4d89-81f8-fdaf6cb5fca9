import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

// Initialize Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
});

export async function POST(request: NextRequest) {
  try {
    console.log('🎨 === AI INPAINTING REQUEST ===');
    const requestBody = await request.json();
    console.log('📦 Request body:', JSON.stringify(requestBody, null, 2));
    
    const { imageUrl, maskData, prompt, selectedArea } = requestBody;

    if (!imageUrl || !maskData || !prompt || !selectedArea) {
      console.log('❌ Missing required parameters');
      return NextResponse.json(
        { error: 'Image URL, mask data, prompt, and selected area are required' },
        { status: 400 }
      );
    }

    if (!process.env.REPLICATE_API_TOKEN) {
      console.log('❌ Replicate API token not configured');
      return NextResponse.json(
        { error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    console.log('🔧 === REPLICATE INPAINTING API CALL ===');
    console.log('🎯 Model: stability-ai/stable-diffusion-inpainting');
    console.log('📝 Prompt:', prompt);
    console.log('📍 Selected area:', selectedArea);

    // The mask data is already provided from the client
    console.log('🎭 Using provided mask data');
    
    const replicateParams = {
      input: {
        image: imageUrl,
        mask: maskData,
        prompt: prompt,
        num_inference_steps: 20,
        guidance_scale: 7.5,
        num_outputs: 1,
        scheduler: "K_EULER_ANCESTRAL"
      }
    };
    
    console.log('⚙️ Parameters being sent to Replicate:', JSON.stringify(replicateParams, null, 2));
    console.log('⏰ Starting inpainting at:', new Date().toISOString());
    
    const output = await replicate.run(
      "stability-ai/stable-diffusion-inpainting",
      replicateParams
    );
    
    console.log('✅ Replicate API response received');
    console.log('📤 Output type:', typeof output);
    console.log('📤 Output:', output);
    
    // Handle the output - it should be an array of URLs
    let resultUrl: string;
    
    if (Array.isArray(output) && output.length > 0) {
      resultUrl = output[0];
    } else if (typeof output === 'string') {
      resultUrl = output;
    } else {
      throw new Error('Unexpected output format from Replicate API');
    }
    
    console.log('🖼️ Final result URL:', resultUrl);
    console.log('⏰ Inpainting completed at:', new Date().toISOString());
    
    return NextResponse.json({
      success: true,
      imageUrl: resultUrl,
      originalPrompt: prompt,
      selectedArea: selectedArea
    });
    
  } catch (error: any) {
    console.error('💥 === INPAINTING ERROR ===');
    console.error('Error details:', error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    
    return NextResponse.json(
      { 
        error: error.message || 'Failed to process AI inpainting',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
